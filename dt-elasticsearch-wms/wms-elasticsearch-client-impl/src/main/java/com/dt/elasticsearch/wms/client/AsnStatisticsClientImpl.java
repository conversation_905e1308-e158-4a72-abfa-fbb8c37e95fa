package com.dt.elasticsearch.wms.client;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.enums.asn.AsnStatDimensionEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.wms.config.IndexNameConfig;
import com.dt.elasticsearch.wms.dto.AsnStatisticsDTO;
import com.dt.elasticsearch.wms.integration.domain.AsnStatisticsIndex;
import com.dt.elasticsearch.wms.param.AsnStatisticsParam;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: yousx
 * @Date: 2025/06/10
 * @Description:
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class AsnStatisticsClientImpl implements IAsnStatisticsClient {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Resource
    private IndexNameConfig indexNameConfig;

    @Override
    public PageVO<AsnStatisticsDTO> getPage(AsnStatisticsParam param) {
        if (param.getAnalysisDimension() == null) {
            throw new BaseException(BaseBizEnum.TIP, "统计维度不能为空");
        }

        // 构建查询条件
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(param.getCargoCode())) {
            queryBuilder.must(QueryBuilders.termQuery("cargoCode.keyword", param.getCargoCode()));
        }
        if (StringUtils.isNotBlank(param.getType())) {
            queryBuilder.must(QueryBuilders.termQuery("type.keyword", param.getType()));
        }
        if (Objects.nonNull(param.getRecDateStart())) {
            queryBuilder.must(QueryBuilders.rangeQuery("recDateEnd").gte(param.getRecDateStart()));
        }
        if (Objects.nonNull(param.getRecDateEnd())) {
            queryBuilder.must(QueryBuilders.rangeQuery("recDateEnd").lte(param.getRecDateEnd()));
        }

        // 构建聚合查询
        TermsAggregationBuilder aggregationBuilder = buildAggregation(param.getAnalysisDimension());

        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(indexNameConfig.getAsnStatisticsIndexName())
                .withQuery(queryBuilder)
                .addAggregation(aggregationBuilder)
                .withPageable(PageRequest.of(param.getCurrent() == 0 ? 0 : param.getCurrent() - 1, param.getSize()))
                .build();

        // 执行聚合查询
        AggregatedPage<AsnStatisticsIndex> page = elasticsearchRestTemplate.queryForPage(searchQuery, AsnStatisticsIndex.class);

        // 处理聚合结果
        List<AsnStatisticsDTO> resultList = processAggregationResult(page.getAggregations(), param.getAnalysisDimension());

        // 构建返回结果
        PageVO<AsnStatisticsDTO> pageVO = new PageVO<>();
        PageVO.Page pageInfo = new PageVO.Page();
        pageInfo.setCurrentPage(1L);
        pageInfo.setPageSize((long) resultList.size());
        pageInfo.setTotalCount((long) resultList.size());
        pageInfo.setTotalPage(1L);
        pageVO.setPage(pageInfo);
        pageVO.setDataList(resultList);

        return pageVO;
    }

    /**
     * 构建聚合查询
     */
    private TermsAggregationBuilder buildAggregation(String analysisDimension) {
        // 默认按 statDate 分组
        TermsAggregationBuilder statDateAgg = AggregationBuilders.terms("statDateGroup")
                .field("statDate.keyword")
                .size(1000);

        // 添加求和聚合
        statDateAgg.subAggregation(AggregationBuilders.sum("recSkuQtySum").field("recSkuQty"));
        statDateAgg.subAggregation(AggregationBuilders.sum("recSkuTypeSum").field("recSkuType"));
        statDateAgg.subAggregation(AggregationBuilders.sum("volumeSum").field("volume"));
        statDateAgg.subAggregation(AggregationBuilders.sum("grossWeightSum").field("grossWeight"));
        statDateAgg.subAggregation(AggregationBuilders.sum("netWeightSum").field("netWeight"));

        // 如果是 DATE_AND_CARGO 维度，需要再按 cargoCode 分组
        if (AsnStatDimensionEnum.DATE_AND_CARGO.getCode().equals(analysisDimension)) {
            TermsAggregationBuilder cargoCodeAgg = AggregationBuilders.terms("cargoCodeGroup")
                    .field("cargoCode.keyword")
                    .size(1000);

            // 在 cargoCode 分组下添加求和聚合
            cargoCodeAgg.subAggregation(AggregationBuilders.sum("recSkuQtySum").field("recSkuQty"));
            cargoCodeAgg.subAggregation(AggregationBuilders.sum("recSkuTypeSum").field("recSkuType"));
            cargoCodeAgg.subAggregation(AggregationBuilders.sum("volumeSum").field("volume"));
            cargoCodeAgg.subAggregation(AggregationBuilders.sum("grossWeightSum").field("grossWeight"));
            cargoCodeAgg.subAggregation(AggregationBuilders.sum("netWeightSum").field("netWeight"));

            statDateAgg.subAggregation(cargoCodeAgg);
        }

        return statDateAgg;
    }

    /**
     * 处理聚合结果
     */
    private List<AsnStatisticsDTO> processAggregationResult(Aggregations aggregations, String analysisDimension) {
        List<AsnStatisticsDTO> resultList = new ArrayList<>();

        ParsedStringTerms statDateTerms = aggregations.get("statDateGroup");
        if (statDateTerms == null) {
            return resultList;
        }

        for (Terms.Bucket statDateBucket : statDateTerms.getBuckets()) {
            String statDate = statDateBucket.getKeyAsString();

            if (AsnStatDimensionEnum.DATE_AND_CARGO.getCode().equals(analysisDimension)) {
                // 处理 DATE_AND_CARGO 维度
                ParsedStringTerms cargoCodeTerms = statDateBucket.getAggregations().get("cargoCodeGroup");
                if (cargoCodeTerms != null) {
                    for (Terms.Bucket cargoCodeBucket : cargoCodeTerms.getBuckets()) {
                        String cargoCode = cargoCodeBucket.getKeyAsString();
                        AsnStatisticsDTO dto = createDTOFromBucket(cargoCodeBucket, statDate, cargoCode);
                        resultList.add(dto);
                    }
                }
            } else {
                // 处理 DATE 维度
                AsnStatisticsDTO dto = createDTOFromBucket(statDateBucket, statDate, null);
                resultList.add(dto);
            }
        }

        return resultList;
    }

    /**
     * 从聚合桶创建 DTO
     */
    private AsnStatisticsDTO createDTOFromBucket(Terms.Bucket bucket, String statDate, String cargoCode) {
        AsnStatisticsDTO dto = new AsnStatisticsDTO();

        // 设置维度值
        dto.setRecDateEndDesc(statDate);
        if (cargoCode != null) {
            dto.setCargoCode(cargoCode);
        }

        // 获取聚合值
        Aggregations aggregations = bucket.getAggregations();

        // 获取各个求和聚合的结果
        ParsedSum recSkuQtySum = aggregations.get("recSkuQtySum");
        if (recSkuQtySum != null) {
            dto.setRecSkuQty(formatBigDecimal(recSkuQtySum.getValue()));
        } else {
            dto.setRecSkuQty("0");
        }

        ParsedSum recSkuTypeSum = aggregations.get("recSkuTypeSum");
        if (recSkuTypeSum != null) {
            dto.setRecSkuType(String.valueOf((int) recSkuTypeSum.getValue()));
        } else {
            dto.setRecSkuType("0");
        }

        ParsedSum volumeSum = aggregations.get("volumeSum");
        if (volumeSum != null) {
            dto.setVolume(formatBigDecimal(volumeSum.getValue()));
        } else {
            dto.setVolume("0");
        }

        ParsedSum grossWeightSum = aggregations.get("grossWeightSum");
        if (grossWeightSum != null) {
            dto.setGrossWeight(formatBigDecimal(grossWeightSum.getValue()));
        } else {
            dto.setGrossWeight("0");
        }

        ParsedSum netWeightSum = aggregations.get("netWeightSum");
        if (netWeightSum != null) {
            dto.setNetWeight(formatBigDecimal(netWeightSum.getValue()));
        } else {
            dto.setNetWeight("0");
        }

        return dto;
    }

    /**
     * 格式化 BigDecimal 值
     */
    private String formatBigDecimal(double value) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return "0";
        }
        return BigDecimal.valueOf(value).stripTrailingZeros().toPlainString();
    }
}
