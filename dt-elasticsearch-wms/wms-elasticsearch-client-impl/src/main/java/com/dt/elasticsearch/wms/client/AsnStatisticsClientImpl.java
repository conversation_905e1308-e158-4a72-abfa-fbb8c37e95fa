package com.dt.elasticsearch.wms.client;

import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.util.ObjectUtils;

import com.dt.component.common.enums.BaseBizEnum;
import com.dt.component.common.exceptions.BaseException;
import com.dt.component.common.holder.CurrentRouteHolder;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.wms.config.IndexNameConfig;
import com.dt.elasticsearch.wms.dto.AsnStatisticsDTO;
import com.dt.elasticsearch.wms.integration.domain.ShipmentIndex;
import com.dt.elasticsearch.wms.param.AsnStatisticsParam;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: yousx
 * @Date: 2025/06/10
 * @Description:
 */
@Slf4j
@DubboService(version = "${dubbo.service.version}")
public class AsnStatisticsClientImpl implements IAsnStatisticsClient {

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Resource
    private IndexNameConfig indexNameConfig;

    @Override
    public PageVO<AsnStatisticsDTO> getPage(AsnStatisticsParam param) {
        if (param.getAnalysisDimension() == null) {
            throw new BaseException(BaseBizEnum.TIP, "统计维度不能为空");
        }

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(param.getCargoCode())) {
            queryBuilder.must(QueryBuilders.termQuery("cargoCode.keyword", param.getCargoCode()));
        }
        if (StringUtils.isNotBlank(param.getType())) {
            queryBuilder.must(QueryBuilders.termQuery("type.keyword", param.getType()));
        }
        if (Objects.nonNull(param.getRecDateStart())) {
            queryBuilder.must(QueryBuilders.rangeQuery("recDateEnd").gte(param.getRecDateStart()));
        }
        if (Objects.nonNull(param.getRecDateEnd())) {
            queryBuilder.must(QueryBuilders.rangeQuery("recDateEnd").lte(param.getRecDateEnd()));
        }


        SearchQuery build = new NativeSearchQueryBuilder().withIndices(indexNameConfig.getAsnStatisticsIndexName())
                .withQuery(queryBuilder)

                .build();
        AggregatedPage<ShipmentIndex> page = elasticsearchRestTemplate.queryForPage(build, ShipmentIndex.class);
        return null;
    }
}
