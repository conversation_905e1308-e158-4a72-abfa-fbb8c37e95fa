package com.dt.portal.wms.web.vo.asnStatistics;

import java.io.Serializable;

import lombok.Data;

/**
 * @Author: yousx
 * @Date: 2025/06/10
 * @Description:
 */
@Data
public class AsnStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 收货完成时间 统计日期
     */
    private String recDateEndDesc;

    /**
     * 货主编码
     */
    private String cargoCode;
    private String cargoName;

    /**
     * 业务类型
     */
    private String type;
    private String typeName;

    /**
     * 实收数量
     */
    private String recSkuQty;

    /**
     * 实收品种数
     */
    private String recSkuType;

    /**
     * 总体积
     */
    private String volume;

    /**
     * 总毛重
     */
    private String grossWeight;

    /**
     * 总净重
     */
    private String netWeight;
}
