package com.dt.portal.wms.web.client.asnStatistics;

import com.dt.component.common.result.Result;
import com.dt.component.common.vo.PageVO;
import com.dt.elasticsearch.wms.param.AsnStatisticsParam;
import com.dt.portal.wms.web.vo.asnStatistics.AsnStatisticsVO;

/**
 * @Author: yousx
 * @Date: 2025/06/10
 * @Description:
 */
public interface IAsnStatisticsPortalClient {

    Result<PageVO<AsnStatisticsVO>> getPage(AsnStatisticsParam param);
}
